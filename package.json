{"name": "zing-billing-service", "version": "0.0.1", "description": "", "author": "", "license": "MIT", "scripts": {"build": "tsc -p tsconfig.build.json", "format": "prettier --write \"src/**/*.ts\"", "start": "ts-node -r tsconfig-paths/register src/main.ts", "start:dev": "nodemon", "start:debug": "nodemon --config nodemon-debug.json", "start:prod": "node dist/main.js", "lint": "tslint -p tsconfig.json -c tslint.json", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^6.0.0", "@nestjs/core": "^6.0.0", "@nestjs/platform-express": "^6.0.0", "@nestjs/typeorm": "^6.1.3", "@sentry/node": "5.6.2", "dotenv": "^8.1.0", "lodash": "^4.17.15", "moment": "^2.24.0", "nest-raven": "^5.0.0", "nest-schedule": "^0.6.3", "pg": "^8.9.0", "pg-connection-string": "^2.1.0", "reflect-metadata": "^0.1.12", "rimraf": "^2.6.2", "rxjs": "^6.3.3", "typeorm": "^0.2.19"}, "devDependencies": {"@nestjs/testing": "^6.0.0", "@types/express": "^4.16.0", "@types/jest": "^23.3.13", "@types/node": "^10.12.18", "@types/supertest": "^2.0.7", "jest": "^23.6.0", "nodemon": "^1.18.9", "prettier": "^1.15.3", "supertest": "^3.4.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "tsconfig-paths": "^3.7.0", "tslint": "5.12.1", "typescript": "^3.2.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}, "engines": {"node": "18.x"}}