import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  ManyToOne,
  Index,
} from 'typeorm';
import { Coupon } from '../coupon/coupon.entity';
import { Tenant } from '../tenant/tenant.entity';

@Entity()
export class Order {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(type => Tenant, tenant => tenant.orders)
  tenant: Tenant;

  @Column()
  @Index()
  externalOrderId: string;

  @Column({ nullable: true })
  @Index()
  externalCustomerId: string;

  @ManyToMany(type => Coupon, coupon => coupon.redemptionOrders)
  redeemedCoupons: Coupon[];

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
  })
  totalPrice: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
  })
  subtotalPrice: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
  })
  totalTax: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
  })
  totalDiscounts: number;

  // Differentiate zing coupon discounts from others
  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
  })
  zingDiscounts: number;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  externalCreatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  externalPaymentAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
