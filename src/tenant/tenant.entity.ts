import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Billing } from '../billing/billing.entity';
import { Order } from '../order/order.entity';
import { Coupon } from '../coupon/coupon.entity';

@Entity()
export class Tenant {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('varchar')
  platform: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  externalId: string | number;

  @Column('varchar')
  name: string;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 5,
    default: 0.05,
  })
  couponPercentage: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 5,
    default: 0.8,
  })
  minimumGrossPercentage: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 7,
    default: 0.015,
  })
  billingPercentage: number;

  @Column({ default: 10 })
  reservationMinutes: number;

  @Column({
    type: 'integer',
    default: 1,
  })
  queueBatchSize: number;

  @Column({
    type: 'json',
    default: {},
  })
  featureFlags?: any;

  @Column({
    type: 'date',
    nullable: true,
  })
  trialEndsOn?: Date;

  @OneToMany(type => Coupon, coupons => coupons.tenant)
  coupons: Coupon[];

  @OneToMany(type => Order, orders => orders.tenant)
  orders: Order[];

  @OneToMany(type => Billing, billing => billing.tenant)
  billing: Billing[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
