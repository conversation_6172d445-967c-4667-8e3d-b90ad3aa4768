import { Modu<PERSON> } from '@nestjs/common';
import { ScheduleModule } from 'nest-schedule';
import { parse } from 'pg-connection-string';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { BillingModule } from './billing/billing.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BillingJobRunnerModule } from './billing-job-runner/billing-job-runner.module';

let connectionConfig;

if (process.env.DATABASE_URL) {
  const connection = parse(process.env.DATABASE_URL);

  connectionConfig = {
    type: process.env.TYPEORM_CONNECTION,
    host: connection.host,
    port: connection.port,
    username: connection.user,
    password: connection.password,
    database: connection.database,
    entities: [process.env.TYPEORM_ENTITIES],
    synchronize: false,
    extra: {
      /*
 rejectUnauthorized explicitly needed here because of updates in node 12.22.5, see node changelog for deets
 https://github.com/nodejs/node/blob/master/doc/changelogs/CHANGELOG_V12.md#2021-08-11-version-12225-erbium-lts-bethgriggs
 */
      ssl:
        typeof process.env.TYPEORM_SSL === 'undefined' ||
        process.env.TYPEORM_SSL === 'true'
          ? { rejectUnauthorized: false }
          : false,
    },
  };
}

@Module({
  imports: [
    TypeOrmModule.forRoot(connectionConfig),
    ScheduleModule.register(),
    BillingJobRunnerModule,
    BillingModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
