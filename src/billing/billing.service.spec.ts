import { Test, TestingModule } from '@nestjs/testing';
import { BillingService } from './billing.service';
import { Billing } from './billing.entity';
import { MockBillingRepository } from './mock/repository';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Order } from '../order/order.entity';
import { MockOrderRepository } from '../order/mock/repository';
import { MockTenantRepository } from '../tenant/mock/repository';
import { Tenant } from '../tenant/tenant.entity';

describe('BillingService', () => {
  let service: BillingService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BillingService,
        {
          provide: getRepositoryToken(Billing),
          useClass: MockBillingRepository,
        },
        {
          provide: getRepositoryToken(Tenant),
          useClass: MockTenantRepository,
        },
        {
          provide: getRepositoryToken(Order),
          useClass: MockOrderRepository,
        },
      ],
    }).compile();

    service = module.get<BillingService>(BillingService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('#createPendingBill()', () => {
    it('should be defined', () => {
      expect(service.createPendingBill).toBeDefined();
    });
  });

  describe('#calculateBillingForDate()', () => {
    it('should be defined', () => {
      expect(service.calculateBillingForDate).toBeDefined();
    });
  });
});
