import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  Index,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { Tenant } from '../tenant/tenant.entity';
import { Order } from '../order/order.entity';

@Entity()
export class Coupon {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 5,
  })
  percentage: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
    nullable: true,
  })
  minimumCartSubtotal: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
  })
  couponAmount: number;

  @Index({ unique: true })
  @Column('varchar')
  code: string;

  // This will generally be the optin.id (uuid)
  @Index()
  @Column({
    type: 'varchar',
    nullable: true,
  })
  reservedId: string;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  reservedUntil: Date;

  // If fallback=false, this will only contain one redemption per coupon
  // Else, if fallback=true, that can contain any number of redemptions
  @ManyToMany(type => Order, order => order.redeemedCoupons)
  @JoinTable()
  redemptionOrders: Order[];

  @OneToOne(type => Order)
  @JoinColumn()
  originatorOrder: Order;

  @Column({ default: false })
  isDisabled: boolean;

  @Column({ default: false })
  isFallback: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Need this so we can access the join column without loading the relationship
  @Column({ nullable: true })
  tenantId: string;

  @ManyToOne(type => Tenant, tenant => tenant.coupons)
  tenant: Tenant;
}
